# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import sys
import os
import psycopg2
import psycopg2.extras
import struct
import pytz
from pytz import AmbiguousTimeError
import logging
from datetime import datetime
from pathlib import Path

# 重新设置项目根目录
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent  # 回退到TradeFusion目录

# 确保项目根目录在系统路径中
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("接力")

try:
    from 公共模块.交易日期 import get_trading_date
except ImportError as e:
    sys.exit(1)

# TradeFusion智能日志系统 - 稍后从全局角度重新设计

def generate_timestamp(trading_date):
    """生成北京时间8点对应的UTC时间戳"""
    tz_sh = pytz.timezone('Asia/Shanghai')
    naive_dt = datetime(
        trading_date.year,
        trading_date.month,
        trading_date.day,
        8, 0, 0
    )
    try:
        localized_dt = tz_sh.localize(naive_dt, is_dst=None)
    except AmbiguousTimeError:
        localized_dt = tz_sh.localize(naive_dt, is_dst=False)
    return int(localized_dt.astimezone(pytz.utc).timestamp())

def read_dat_file(filepath, target_ts=None):
    """读取DAT文件内容，如果指定了target_ts，则只返回该时间戳的值"""
    result = {}
    if not os.path.exists(filepath):
        return result
        
    try:
        with open(filepath, 'rb') as f:
            while True:
                record = f.read(8)
                if not record or len(record) != 8:
                    break
                    
                ts = struct.unpack('<I', record[:4])[0]
                value = struct.unpack('<f', record[4:])[0]
                
                if target_ts is None or ts == target_ts:
                    result[ts] = value
                    if target_ts == ts:  # 如果找到指定时间戳，可以提前返回
                        break
        
        return result
    except Exception as e:
        return {}

def update_dat_file(filepath, target_ts, new_value):
    """更新或追加dat文件记录"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 标记操作类型
        operation_type = "追加记录"
        
        if os.path.exists(filepath):
            with open(filepath, 'rb+') as f:
                found = False
                while True:
                    pos = f.tell()
                    record = f.read(8)
                    if not record:
                        break
                    if len(record) != 8:
                        break
                    ts = struct.unpack('<I', record[:4])[0]
                    if ts == target_ts:
                        f.seek(pos + 4)
                        f.write(struct.pack('<f', new_value))
                        found = True
                        operation_type = "更新记录"
                        break
                if not found:
                    f.seek(0, 2)
                    f.write(struct.pack('<I', target_ts) + struct.pack('<f', new_value))
        else:
            with open(filepath, 'wb') as f:
                f.write(struct.pack('<I', target_ts) + struct.pack('<f', new_value))
                operation_type = "创建新文件"
        
        # 简化验证写入逻辑
        verification = read_dat_file(filepath, target_ts)
        if target_ts in verification:
            written_value = verification[target_ts]
            if int(written_value) != int(new_value):
                return False
            return True
        else:
            return False
    except Exception as e:
        return False

# {{ AURA-X: Modify - 修改为PostgreSQL表检查语法. Approval: 寸止(ID:1737734400). }}
def check_table_exists(conn, table_name):
    """检查表是否存在"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT table_name FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = %s
    """, (table_name,))
    return cursor.fetchone() is not None

def main():
    # 导入配置管理
    # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        dat_dir = os.path.join(config.get('data_sources.dzh_base_path'), '接力')
    except ImportError as e:
        dat_dir = r'E:\dzh2\USERDATA\SelfData\接力'

    # {{ AURA-X: Modify - 统一PostgreSQL密码为标准密码. Approval: 寸止(ID:1737734400). }}
    # {{ AURA-X: Modify - 使用统一配置管理而非硬编码. Approval: 寸止(ID:1737734400). }}
    try:
        from 公共模块.配置管理 import get_config
        config = get_config()
        db_config = config.get('database')
    except ImportError:
        # 备用配置
        db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'tradefusion',
            'user': 'postgres',
            'password': 'ymjatTUU520'
        }
    
    # 确保输出目录存在
    if not os.path.exists(dat_dir):
        try:
            os.makedirs(dat_dir)
        except Exception as e:
            return False, f"创建输出目录失败: {e}", 0
    
    # 获取交易日
    try:
        date_obj = datetime.strptime(str(get_trading_date()), '%Y%m%d').date()
        current_date = int(date_obj.strftime('%Y%m%d'))
    except Exception as e:
        return False, f"获取交易日失败: {e}", 0

    # {{ AURA-X: Modify - 修改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
    # 连接数据库
    processed_count = 0
    cleared_count = 0
    conn = None
    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        # 检查个股接力表是否存在
        if not check_table_exists(conn, "个股接力表"):
            return False, "个股接力表不存在", 0
        
        # {{ AURA-X: Modify - 修复PostgreSQL语法错误. Approval: 寸止(ID:1737734400). }}
        # 创建DAT文件处理记录表
        cursor.execute('''CREATE TABLE IF NOT EXISTS "DAT文件处理记录表" (
            "日期" INTEGER,
            "股票代码" TEXT,
            PRIMARY KEY ("日期", "股票代码")
        )''')

        # 清理过期数据
        cursor.execute('DELETE FROM "DAT文件处理记录表" WHERE "日期" != %s', (current_date,))

        # 获取接力数据
        cursor.execute('''
            SELECT "股票代码", "接力"
            FROM "个股接力表"
            WHERE "日期" = %s AND "接力" IS NOT NULL
        ''', (current_date,))
        
        records = cursor.fetchall()
        
        if not records:
            return False, f"未找到当日接力数据", 0
        
        
        # 处理记录
        target_ts = generate_timestamp(date_obj)
        processed_stocks = set()
        min_value = float('inf')
        max_value = float('-inf')
        problematic_data = []
        
        # 批量处理记录
        for stock_code, jielie in records:
            try:
                # 数据验证
                jielie_value = float(jielie)
                if not (0 <= jielie_value <= 100):
                    continue  # 跳过无效数据

                min_value = min(min_value, jielie_value)
                max_value = max(max_value, jielie_value)
                
                filepath = os.path.join(dat_dir, f"{stock_code.strip()}.dat")
                
                # 检查文件是否已经存在，如果存在，读取当前值
                existing_data = read_dat_file(filepath, target_ts)
                if target_ts in existing_data and int(existing_data[target_ts]) != int(jielie_value):
                    problematic_data.append((stock_code, jielie_value, existing_data[target_ts]))
                
                if update_dat_file(filepath, target_ts, jielie_value):
                    # {{ AURA-X: Modify - 修复PostgreSQL语法错误. Approval: 寸止(ID:1737734400). }}
                    # 更新DAT文件处理记录表
                    cursor.execute('''
                        INSERT INTO "DAT文件处理记录表" ("日期", "股票代码")
                        VALUES (%s, %s)
                        ON CONFLICT ("日期", "股票代码") DO NOTHING
                    ''', (current_date, stock_code))
                    processed_count += 1
                    processed_stocks.add(stock_code)
                    
                    # 每100条记录提交一次，避免事务过大
                    if processed_count % 100 == 0:
                        conn.commit()
            except ValueError as e:
                continue  # 跳过数值转换错误
            except Exception as e:
                continue  # 跳过其他错误
        
        # {{ AURA-X: Modify - 修复PostgreSQL语法错误. Approval: 寸止(ID:1737734400). }}
        # 处理需要清零的股票
        cursor.execute('SELECT "股票代码" FROM "DAT文件处理记录表" WHERE "日期" = %s', (current_date,))
        all_stocks = {row[0] for row in cursor.fetchall()}
        stocks_to_clear = all_stocks - processed_stocks
        
        for stock_code in stocks_to_clear:
            try:
                filepath = os.path.join(dat_dir, f"{stock_code.strip()}.dat")
                if update_dat_file(filepath, target_ts, 0.0):
                    cleared_count += 1
            except Exception as e:
                continue  # 跳过清零错误

        conn.commit()

        if min_value != float('inf') and max_value != float('-inf'):
            pass  # 数据范围验证通过
        
        # 方案B：统计汇总模式 - 接力值动态更新统计
        if problematic_data:
            total_updates = len(problematic_data)
            # 计算平均变化幅度
            avg_change = sum(abs(db_val - dat_val) for _, db_val, dat_val in problematic_data) / total_updates
            # 分析变化趋势
            increases = sum(1 for _, db_val, dat_val in problematic_data if db_val > dat_val)
            decreases = total_updates - increases

            # 统计汇总输出

            # 详细记录已移除
        
        return True, date_obj.strftime('%Y-%m-%d'), processed_count + cleared_count

    # {{ AURA-X: Modify - 修改为PostgreSQL异常处理. Approval: 寸止(ID:1737734400). }}
    except psycopg2.Error as e:
        if conn:
            conn.rollback()
        return False, str(e), 0
    except Exception as e:
        if conn:
            conn.rollback()
        return False, str(e), 0
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    success, message, total_count = main()
    result_msg = f"接力数据DAT文件更新 {message} {'成功' if success else '失败'}" + (f" (更新/清零: {total_count} 条)" if success else "")
    if success:
        logger.记录模块执行(result_msg, total_count)
        print(result_msg)
    else:
        logger.记录错误(result_msg)
        print(result_msg)
