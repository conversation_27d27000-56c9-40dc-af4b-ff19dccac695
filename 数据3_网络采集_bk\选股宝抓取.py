import sys
import os
from pathlib import Path

# 动态获取项目根目录
if getattr(sys, 'frozen', False):
    PROJECT_ROOT = Path(sys.executable).parent
else:
    PROJECT_ROOT = Path(__file__).absolute().parent.parent

# 验证工作目录
if not PROJECT_ROOT.exists():
    print(f"错误：项目根目录不存在 - {PROJECT_ROOT}")
    sys.exit(1)

# 添加项目根目录到Python路径
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

import csv
import time
from datetime import datetime, time as dt_time

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("选股宝抓取")

# {{ AURA-X: Modify - 摆脱浏览器管理器依赖，使用原生Playwright API. Approval: 寸止(ID:1738320024). }}
# 使用原生Playwright API
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erContex<PERSON>, Page, Playwright
from typing import Optional
import atexit

# ========== 全局配置 ==========
# 定义路径
LOGS_PATH = PROJECT_ROOT / "logs"
# 确保目录存在
LOGS_PATH.mkdir(parents=True, exist_ok=True)
TARGET_URL = "https://xuangutong.com.cn/top-gainer"

# {{ AURA-X: Modify - 使用项目本地浏览器路径. Approval: 寸止(ID:1738320024). }}
# 浏览器持久化配置
BROWSER_USER_DATA_DIR = PROJECT_ROOT / "browser_data" / "xuangubao"
BROWSER_USER_DATA_DIR.mkdir(parents=True, exist_ok=True)

# 项目本地浏览器路径
LOCAL_BROWSER_PATH = PROJECT_ROOT / "browsers" / "chromium-1181" / "chrome-win" / "chrome.exe"

# ========== 时间控制配置 ==========
# 禁止运行时段：8:40-9:24（开盘前敏感时段）
FORBIDDEN_START_TIME = dt_time(8, 40)  # 8:40
FORBIDDEN_END_TIME = dt_time(9, 24)    # 9:24

def is_forbidden_time():
    """检查当前时间是否在禁止运行时段内"""
    current_time = datetime.now().time()

    # 检查是否在禁止时段内
    if FORBIDDEN_START_TIME <= current_time <= FORBIDDEN_END_TIME:
        return True, f"当前时间 {current_time.strftime('%H:%M:%S')} 在禁止运行时段内 ({FORBIDDEN_START_TIME.strftime('%H:%M')}-{FORBIDDEN_END_TIME.strftime('%H:%M')})"

    return False, None

def wait_until_allowed_time():
    """等待直到允许运行的时间"""
    is_forbidden, reason = is_forbidden_time()

    if is_forbidden:
        logger.记录模块执行(f"⏰ {reason}")

        # 计算需要等待的时间
        current_time = datetime.now().time()
        current_datetime = datetime.now()

        # 构造今天的禁止结束时间
        forbidden_end_datetime = current_datetime.replace(
            hour=FORBIDDEN_END_TIME.hour,
            minute=FORBIDDEN_END_TIME.minute,
            second=0,
            microsecond=0
        )

        # 如果当前时间已经过了禁止结束时间，说明是跨天的情况（理论上不会发生，但保险起见）
        if current_datetime > forbidden_end_datetime:
            logger.记录模块执行("时间检查异常，继续执行")
            return

        # 计算等待秒数
        wait_seconds = (forbidden_end_datetime - current_datetime).total_seconds()
        wait_minutes = int(wait_seconds / 60)

        logger.记录模块执行(f"⏳ 等待 {wait_minutes} 分钟后恢复运行 (将在 {FORBIDDEN_END_TIME.strftime('%H:%M')} 后继续)")

        # 等待到允许时间
        time.sleep(wait_seconds)

        logger.记录模块执行(f"✅ 禁止时段结束，恢复正常运行")

# {{ AURA-X: Modify - 修复全局变量状态不一致，删除未使用的_global_context. Approval: 寸止(ID:1738320024). }}
# 全局浏览器实例（持久化）
# 注意：_global_browser 实际上是 BrowserContext 对象，不是 Browser 对象
# 这是因为 launch_persistent_context() 返回的是 BrowserContext
_global_playwright = None
_global_browser = None  # 实际类型：BrowserContext

def _get_persistent_browser():
    """获取持久化浏览器上下文实例"""
    # {{ AURA-X: Modify - 修复全局变量引用，删除未使用的_global_context. Approval: 寸止(ID:1738320024). }}
    global _global_playwright, _global_browser

    try:
        # 检查现有实例是否有效
        if _global_browser:
            try:
                # 测试浏览器上下文是否仍然可用
                _global_browser.pages
                return _global_browser
            except:
                # 浏览器已失效，需要重新创建
                _cleanup_browser()

        # 创建新的浏览器实例
        if _global_playwright is None:
            _global_playwright = sync_playwright().start()

        # {{ AURA-X: Modify - 使用项目本地浏览器路径启动. Approval: 寸止(ID:1738320024). }}
        # 使用项目本地浏览器和持久化用户数据目录
        _global_browser = _global_playwright.chromium.launch_persistent_context(
            user_data_dir=str(BROWSER_USER_DATA_DIR),
            executable_path=str(LOCAL_BROWSER_PATH),  # 指定本地浏览器路径
            headless=True,  # 无头模式，提高性能和稳定性
            args=[
                "--no-sandbox",
                "--disable-blink-features=AutomationControlled",
                "--force-device-scale-factor=1",
                "--lang=zh-CN",
                "--ignore-certificate-errors"
            ],
            viewport={"width": 1366, "height": 768},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            locale="zh-CN",
            timezone_id="Asia/Shanghai"
        )

        # 添加反检测脚本
        _global_browser.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
            });
            window.chrome = { runtime: {} };
        """)

        # {{ AURA-X: Modify - 删除冗余赋值，persistent_context返回BrowserContext对象. Approval: 寸止(ID:1738320024). }}
        logger.记录模块执行("持久化浏览器上下文创建成功")

        return _global_browser

    except Exception as e:
        logger.记录错误("创建持久化浏览器失败", e)
        _cleanup_browser()
        raise

def _cleanup_browser():
    """清理浏览器资源"""
    # {{ AURA-X: Modify - 修复全局变量清理逻辑，删除未使用的_global_context. Approval: 寸止(ID:1738320024). }}
    global _global_playwright, _global_browser

    try:
        if _global_browser:
            _global_browser.close()
        if _global_playwright:
            _global_playwright.stop()
    except:
        pass
    finally:
        _global_playwright = None
        _global_browser = None

# 注册程序退出时的清理函数
atexit.register(_cleanup_browser)

# ========== 核心功能类 ==========
class StockCrawler:
    def __init__(self):
        # {{ AURA-X: Modify - 修复浏览器实例访问冲突，添加浏览器引用. Approval: 寸止(ID:1738320024). }}
        self._browser: Optional[BrowserContext] = None
        self._page: Optional[Page] = None
        self._init_browser()
        self._bootstrap()

    def _ensure_page(self) -> Page:
        """确保页面对象存在"""
        if self._page is None:
            raise Exception("页面对象未初始化")
        return self._page

    def _init_browser(self):
        """初始化持久化浏览器"""
        # {{ AURA-X: Modify - 修复浏览器实例访问冲突，保存浏览器引用. Approval: 寸止(ID:1738320024). }}
        try:
            # 获取并保存全局浏览器上下文引用
            self._browser = _get_persistent_browser()
            logger.记录模块执行("浏览器初始化成功")

        except Exception as e:
            logger.记录错误("浏览器初始化失败", e)
            raise

    def _bootstrap(self):
        """加载目标网页"""
        # {{ AURA-X: Modify - 修复浏览器实例访问冲突，使用实例引用. Approval: 寸止(ID:1738320024). }}
        try:
            # 创建新页面（使用实例浏览器引用）
            if self._browser:
                self._page = self._browser.new_page()

                # 导航到目标URL
                self._page.goto(TARGET_URL, wait_until="networkidle", timeout=30000)

            # 选股宝网站的股票数据容器
            page = self._ensure_page()
            page.wait_for_selector("#nuxt-layout-container > div > div.topgainer-content > div.topgainer-content-left > div",
                                        state="visible",
                                        timeout=30000)

            logger.记录模块执行("页面加载成功")

        except Exception as e:
            logger.记录错误("页面加载失败", e)
            # {{ AURA-X: Modify - 移除对已删除的_error_handler的调用. Approval: 寸止(ID:1738320024). }}
            # 系统初始化失败，抛出异常让上层处理
            raise RuntimeError(f"系统初始化失败: {str(e)}")

    def fetch_data(self, retries=3):
        """抓取股票数据"""

        for attempt in range(1, retries + 1):
            try:
                # {{ AURA-X: Modify - 修复页面刷新时机冲突，统一刷新策略. Approval: 寸止(ID:1738320024). }}
                # 第一次尝试不刷新（页面已在_bootstrap中加载），重试时才刷新
                page = self._ensure_page()
                if attempt > 1:
                    page.reload(wait_until="networkidle")
                time.sleep(5 + attempt * 2)  # 增加等待时间，随重试次数递增

                # 检查页面是否完全加载
                page.wait_for_load_state("domcontentloaded")

                # 检查目标元素是否存在
                data_selector = "#nuxt-layout-container > div > div.topgainer-content > div.topgainer-content-left > div"
                element = page.wait_for_selector(data_selector, state="visible", timeout=15000)

                if not element:
                    raise ValueError("未找到数据元素")

                # 获取元素文本内容
                text_content = element.inner_text()

                # 验证元素内容不为空
                if not text_content.strip():
                    raise ValueError("抓取到的数据为空")

                # {{ AURA-X: Modify - 简化成功日志，只在最终成功时记录. Approval: 寸止(ID:1738320024). }}
                logger.记录模块执行("数据抓取成功", len(text_content))
                return text_content.strip()

            except Exception as e:
                # {{ AURA-X: Modify - 增强错误处理，记录详细错误信息和重试策略. Approval: 寸止(ID:1735002400). }}
                error_msg = f"第{attempt}次抓取失败: {str(e)}"
                logger.记录错误(error_msg)

                if attempt == retries:
                    logger.记录错误(f"所有{retries}次重试均失败，开始保存调试信息")

                    # 保存截图和页面源码用于调试
                    try:
                        page = self._ensure_page()
                        screenshot_path = LOGS_PATH / "xgb_error.png"
                        page.screenshot(path=str(screenshot_path))
                        logger.记录模块执行("错误截图已保存")

                        html_path = LOGS_PATH / "xgb_error.html"
                        with open(html_path, "w", encoding="utf-8") as f:
                            f.write(page.content())
                        logger.记录模块执行("错误页面源码已保存")

                    except Exception as ss_error:
                        logger.记录错误("保存调试信息失败", ss_error)

                    return None

                # {{ AURA-X: Modify - 改进重试策略，根据错误类型采用不同的恢复方法. Approval: 寸止(ID:1735002400). }}
                logger.记录模块执行(f"准备第{attempt + 1}次重试，采用智能恢复策略")

                # {{ AURA-X: Modify - 简化重试策略，合并相同的处理逻辑. Approval: 寸止(ID:1738320024). }}
                # 智能刷新策略
                page = self._ensure_page()
                if "element not found" in str(e).lower() or "element not visible" in str(e).lower():
                    logger.记录模块执行("检测到元素缺失，重新导航到目标页面")
                    page.goto(TARGET_URL, wait_until="networkidle")
                else:
                    # timeout和其他错误都使用reload
                    logger.记录模块执行("执行页面重载")
                    page.reload(wait_until="networkidle")

                time.sleep(3)

    # {{ AURA-X: Delete - 删除冗余的错误处理方法，重试逻辑已集成到fetch_data中. Approval: 寸止(ID:1738320024). }}
    # _error_handler方法已删除，错误处理逻辑已集成到fetch_data的重试机制中

    def run_service(self):
        """运行爬虫服务"""
        try:
            # {{ AURA-X: Modify - 添加服务运行状态日志，便于问题追踪. Approval: 寸止(ID:1735002400). }}
            logger.记录模块执行("开始运行爬虫服务")

            data = self.fetch_data()
            if data:
                logger.记录模块执行("数据抓取成功，开始保存数据")
                self.save_data(data)
                logger.记录模块执行("爬虫服务执行完成")
                return True
            else:
                logger.记录错误("数据抓取失败，爬虫服务执行失败")
                return False
        except Exception as e:
            # {{ AURA-X: Add - 添加异常捕获，防止未处理的异常导致服务失败. Approval: 寸止(ID:1735002400). }}
            logger.记录错误("爬虫服务执行异常", e)
            return False
        finally:
            # 在run_service中不关闭浏览器，由main函数统一关闭
            pass

    # ========== 数据保存方法 ==========
    def save_data(self, raw_data):
        """保存数据到临时表（主要）和CSV文件（备用）"""
        # {{ AURA-X: Modify - 修复异常处理范围冲突，分离临时表写入和清洗模块触发. Approval: 寸止(ID:1738320024). }}
        try:
            # 主要方案：写入临时表
            from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
            temp_manager = 获取临时表管理器()
            temp_manager.写入选股宝原始数据(raw_data)
            temp_manager.close()

            # 网络采集成功日志（符合TradeFusion统一日志标准）
            logger.记录模块执行("网站数据采集完成", len(raw_data))

        except Exception as e:
            logger.记录错误("写入临时表失败，使用备用CSV方案", e)
            # 备用方案：保存CSV文件
            SAVE_PATH = PROJECT_ROOT / "数据3_网络采集_bk" / "选股宝数据.csv"
            if SAVE_PATH.exists():
                SAVE_PATH.unlink()

            with SAVE_PATH.open("w", encoding="utf-8", newline='') as f:
                writer = csv.writer(f)
                writer.writerow(["股票名称", "最新价", "涨幅", "换手率"])
                count = 0
                for line in raw_data.split('\n'):
                    if line.strip():
                        writer.writerow(line.split())
                        count += 1
            # 备用方案成功日志（符合TradeFusion统一日志标准）
            logger.记录模块执行("网站数据采集完成(备用CSV)", count)

        # 🔄 数据保存完成后，独立触发清洗模块（不受临时表异常影响）
        try:
            self._trigger_cleaning_module()
        except Exception as e:
            logger.记录错误("触发清洗模块失败", e)

    def _trigger_cleaning_module(self):
        """触发选股宝清洗模块"""
        try:
            # 导入清洗模块的主要处理函数
            from 数据3_网络采集_bk.选股宝清洗 import run_cleaner_from_temp_table

            logger.记录模块执行("自动触发清洗模块")

            # 调用清洗函数
            result = run_cleaner_from_temp_table()

            if result is not None:
                logger.记录模块执行("清洗模块执行成功", result)
                # {{ AURA-X: Delete - 删除重复调用，清洗模块已自动触发下游模块. Approval: 寸止(ID:1735002000). }}
                # 清洗模块会自动触发下游处理模块，无需重复调用
            else:
                logger.记录错误("清洗模块执行失败")

        except Exception as e:
            logger.记录错误("触发清洗模块异常", e)

    # {{ AURA-X: Delete - 删除重复调用方法，避免与清洗模块的自动触发冲突. Approval: 寸止(ID:1735002000). }}
    # _trigger_next_modules 方法已删除，因为清洗模块会自动触发下游处理模块

    def close_browser(self):
        """清理页面资源（保持浏览器持久化）"""
        # {{ AURA-X: Modify - 修复循环模式下的浏览器关闭问题. Approval: 寸止(ID:1738320024). }}
        try:
            # 关闭当前页面
            if self._page:
                self._page.close()
                self._page = None

            # 重置浏览器引用，让下次重新获取
            self._browser = None

            # 注意：不关闭全局浏览器实例，因为它们是持久化的
            # 全局实例会在程序退出时通过atexit自动清理
            logger.记录模块执行("页面资源清理完成")

        except Exception as e:
            logger.记录错误("页面资源清理失败", e)


# ========== 主程序 ==========
# {{ AURA-X: Modify - 修复返回值类型冲突，更正函数签名. Approval: 寸止(ID:1738320024). }}
def main() -> bool:
    crawler = None
    try:
        # {{ AURA-X: Modify - 增强主函数错误处理和状态跟踪. Approval: 寸止(ID:1735002400). }}
        logger.记录模块执行("开始初始化选股宝爬虫")
        crawler = StockCrawler()
        logger.记录模块执行("爬虫初始化成功")

        if crawler.run_service():
            # 数据抓取完成，写入临时表后由触发器系统自动处理后续流程
            logger.记录模块执行("网站数据采集完成 - 数据已写入临时表")
            return True  # 返回成功状态
        else:
            logger.记录错误("爬虫服务执行失败")
            return False
    except Exception as e:
        # {{ AURA-X: Modify - 改进异常处理，记录详细错误信息. Approval: 寸止(ID:1735002400). }}
        logger.记录错误("选股宝爬虫主函数执行异常", e)
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 只在这里关闭页面，避免重复关闭（保持浏览器持久化）
        if crawler:
            try:
                logger.记录模块执行("开始清理页面资源")
                crawler.close_browser()
                logger.记录模块执行("页面资源清理完成（浏览器保持持久化）")
            except Exception as e:
                # 捕获并打印错误，但不抛出异常
                logger.记录错误("浏览器资源清理失败", e)

def run_continuous():
    """循环运行模式：每60秒执行一次"""
    # {{ AURA-X: Modify - 删除冗余导入，time已在顶部导入. Approval: 寸止(ID:1738320024). }}

    cycle_count = 0
    success_count = 0
    fail_count = 0

    logger.记录模块执行("循环模式启动 - 60秒间隔")

    try:
        while True:
            cycle_count += 1
            start_time = time.time()

            # 检查是否在禁止运行时段
            wait_until_allowed_time()

            try:
                result_path = main()
                if result_path:
                    success_count += 1
                    logger.记录模块执行(f"第{cycle_count}次执行成功")
                else:
                    fail_count += 1
                    logger.记录错误(f"第{cycle_count}次执行失败")
            except Exception as e:
                fail_count += 1
                logger.记录错误(f"第{cycle_count}次执行异常", e)

            # 计算等待时间（60秒）
            execution_time = time.time() - start_time
            sleep_time = max(0, 60 - execution_time)

            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                logger.记录错误(f"执行时间{execution_time:.1f}秒超过60秒间隔")

    except KeyboardInterrupt:
        logger.记录模块执行("用户中断，正在退出")
    except Exception as e:
        logger.记录错误("循环运行异常", e)
    finally:
        logger.记录模块执行(f"循环结束汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败")

if __name__ == "__main__":
    # {{ AURA-X: Modify - 删除冗余导入，sys已在顶部导入. Approval: 寸止(ID:1738320024). }}

    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        # 单次执行模式
        # 检查是否在禁止运行时段
        is_forbidden, reason = is_forbidden_time()
        if is_forbidden:
            logger.记录模块执行(f"⏰ {reason}")
            logger.记录模块执行("单次执行模式在禁止时段内，程序退出")
            sys.exit(0)

        result_path = main()
        if result_path:
            pass
        else:
            sys.exit(1)
    else:
        # 循环执行模式（默认）
        run_continuous()