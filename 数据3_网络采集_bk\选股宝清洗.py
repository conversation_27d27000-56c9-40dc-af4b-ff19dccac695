import re
import os
import sys
import time
import logging
from pathlib import Path
from typing import Optional, Union
from logging.handlers import TimedRotatingFileHandler

# 重新设置项目根目录
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent  # 回退到YMJATTUU目录

# 确保项目根目录在系统路径中
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("选股宝清洗")

try:
    from 公共模块.加代码前缀 import process_stock_code_prefix
    from 公共模块.交易日期 import get_trading_date
except ImportError as e:
    logger.记录错误("模块导入失败", e)
    sys.exit(1)

class DataCleaner:
    def __init__(self):
        pass

    def parse_stock_tables(self, lines):
        """解析股票表格结构"""
        # 寻找"股票名称"所在行
        headers = []
        for i, line in enumerate(lines):
            if line.strip() == "股票名称":
                headers.append(i)

        if not headers:
            return [], []

        tables = []
        for i, h in enumerate(headers):
            end = headers[i+1] if i+1 < len(headers) else len(lines)
            tables.append((h, end))
        return headers, tables

    def extract_block_info(self, header_pos, lines):
        """提取板块信息"""
        block_name = "未知板块"
        block_change = ""
        reason_line = "无消息"

        # 表头行为"股票名称""最新价"等
        if header_pos >= 1:
            prev_line = lines[header_pos - 1].strip()
            if "理由" in prev_line:
                reason_line = prev_line.replace("理由,", "").replace("理由", "").strip(",").strip()
                # 板块名称为再上一行
                if header_pos >= 2:
                    block_line = lines[header_pos - 2].strip()
                    if "," in block_line and not block_line.startswith("股票名称"):
                        parts = block_line.split(",", 1)
                        block_name = parts[0].strip()
                        block_change = parts[1].strip() if len(parts) > 1 else ""
                    else:
                        block_name = block_line.strip()
            else:
                # 没有理由文本，上一行为板块名称
                block_line = prev_line
                if "," in block_line and not block_line.startswith("股票名称"):
                    parts = block_line.split(",", 1)
                    block_name = parts[0].strip()
                    block_change = parts[1].strip() if len(parts) > 1 else ""
                else:
                    block_name = block_line.strip()

        return {
            "板块名称": block_name,
            "板块涨幅": block_change,
            "板块消息": reason_line
        }

    def process_table(self, start, end, lines):
        """处理表格数据，提取股票信息"""
        stock_data = []
        current_code = None

        i = start
        while i < end:
            line = lines[i].strip()
            # 在新格式中，股票代码通常紧跟在股票名称下方
            if i+1 < end and re.match(r"\d{6}\.", lines[i+1].strip()):
                stock_name = line
                stock_code = lines[i+1].strip().split('.')[0]  # 提取代码部分
                current_code = process_stock_code_prefix(stock_code)
                interpretation = "无解读"
                # 新逻辑：查找"***亿"行，下一行即为解读
                for j in range(i+2, min(i+15, end)):
                    if re.match(r"^[\d\.]+亿$", lines[j].strip()):
                        if j+1 < end:
                            interpretation = lines[j+1].strip().replace("\n", " | ").replace(",", "，")
                        break
                stock_data.append((current_code, interpretation))
                i += 2  # 移动到下一个可能的股票名称位置
            else:
                i += 1

        return stock_data

    def clean_data_from_temp_table(self):
        """从临时表读取数据进行清洗，并写入清洗后的临时表"""
        try:
            from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
            temp_manager = 获取临时表管理器()

            # 从临时表读取原始数据
            raw_data = temp_manager.读取选股宝原始待处理数据()
            if not raw_data:
                logger.记录错误("临时表中无待处理数据")
                temp_manager.close()
                return None

            # 处理原始数据
            lines = [line.strip() for line in raw_data.split('\n') if line.strip()]
            results = []
            headers, tables = self.parse_stock_tables(lines)

            # 使用 公共模块.交易日期 中的 get_trading_date 函数获取交易日期
            trading_date = get_trading_date()

            for header_pos, (start, end) in zip(headers, tables):
                block_info = self.extract_block_info(header_pos, lines)
                # 过滤无消息板块
                if block_info["板块消息"] == "无消息":
                    continue
                # 新增：过滤板块名称为ST股的数据
                if block_info["板块名称"] == "ST股":
                    continue
                stocks = self.process_table(start, end, lines)

                results.extend([
                    (
                        trading_date,
                        code,
                        block_info["板块名称"],
                        block_info["板块涨幅"],
                        block_info["板块消息"],
                        interpretation
                    )
                    for code, interpretation in stocks
                ])

            # 写入清洗后的临时表
            if results:
                temp_manager.写入选股宝清洗数据(results)
                # 标记原始数据为已处理
                temp_manager.标记选股宝原始数据已处理()
                # 符合TradeFusion统一日志标准
                logger.记录模块执行("数据清洗完成", len(results), "选股宝抓取")

            temp_manager.close()

            # 🔄 数据清洗完成，自动触发下游模块
            _trigger_downstream_processing()

            return len(results)

        except Exception as e:
            logger.记录错误("临时表数据清洗失败", e)
            return None

def _trigger_downstream_processing():
    """触发下游处理模块（个股解读_板块信息_关联表）"""
    try:
        # 导入下游模块
        from 数据3_网络采集_bk.个股解读_板块信息_关联表 import main_from_temp_table

        logger.记录模块执行("自动触发下游处理模块")

        # 调用下游模块的主函数
        result = main_from_temp_table()

        if result:
            logger.记录模块执行("下游处理模块执行成功")
        else:
            logger.记录错误("下游处理模块执行失败")

    except Exception as e:
        logger.记录错误("触发下游处理模块异常", e)



def run_cleaner_from_temp_table():
    """从临时表运行清洗器（主要方案）"""
    try:
        # 调用者先输出（精简：移除启动日志）
        # logger.info(f"[选股宝清洗] 开始数据清洗处理 (由[选股宝抓取]调用)")

        cleaner = DataCleaner()  # 临时表模式不需要文件路径
        processed = cleaner.clean_data_from_temp_table()

        if processed is not None and processed > 0:
            return processed
        else:
            logger.记录错误("临时表数据清洗失败")
            return None

    except Exception as e:
        logger.记录错误("临时表清洗异常", e)
        return None

def run_cleaner(input_path: Optional[Path] = None) -> Optional[Union[int, Path]]:
    """运行清洗器（兼容旧接口，优先使用临时表）"""

    # 优先尝试临时表方案
    try:
        result = run_cleaner_from_temp_table()
        if result is not None:
            return result
        return None
    except Exception as e:
        logger.记录错误("临时表方案失败", e)
        return None

if __name__ == "__main__":
    try:
        # 使用临时表方案
        result = run_cleaner_from_temp_table()
        if result is not None:
            logger.记录模块执行("临时表清洗成功", result)
            sys.exit(0)
        else:
            logger.记录错误("临时表方案失败")
            sys.exit(1)

    except Exception as e:
        logger.记录错误("程序异常", e)
        sys.exit(1)