#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion配置管理模块
功能：统一管理项目配置，支持YAML配置文件和环境变量覆盖
作者：TradeFusion团队
版本：v2.0 - PostgreSQL迁移版本
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
import re

class ConfigManager:
    """TradeFusion配置管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.config_file = self.project_root / "config" / "data_sources.yaml"
        self._config_data: Optional[Dict[str, Any]] = None
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    raw_config = yaml.safe_load(f)
                    # 处理环境变量替换
                    self._config_data = self._resolve_env_vars(raw_config)
            else:
                # 使用默认配置
                self._config_data = self._get_default_config()
                print(f"⚠️ 配置文件不存在，使用默认配置: {self.config_file}")
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")
            self._config_data = self._get_default_config()
    
    def _resolve_env_vars(self, config: Any) -> Any:
        """递归解析环境变量"""
        if isinstance(config, dict):
            return {k: self._resolve_env_vars(v) for k, v in config.items()}
        elif isinstance(config, list):
            return [self._resolve_env_vars(item) for item in config]
        elif isinstance(config, str):
            # 匹配 ${VAR_NAME:default_value} 格式
            pattern = r'\$\{([^:}]+):([^}]*)\}'
            matches = re.findall(pattern, config)
            result = config
            for var_name, default_value in matches:
                env_value = os.getenv(var_name, default_value)
                result = result.replace(f'${{{var_name}:{default_value}}}', env_value)
            return result
        else:
            return config
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "data_sources": {
                "dzh_base_path": "E:/dzh2/USERDATA/SelfData",
                "directories": {
                    "lbgd": "10连板高度",
                    "ztdp": "12涨停评分",
                    "ztsj": "11涨停时间",
                    "yzb": "13一字板",
                    "tzb": "14T字板",
                    "hjhs": "15黄金换手",
                    "cjje": "22成交金额"
                }
            },
            "processing": {
                "file_timeout_hours": 12,
                "batch_size": 1000,
                "timezone": "Asia/Shanghai",
                "trading_time": {
                    "market_open_hour": 9,
                    "market_open_minute": 16
                }
            },
            "database": {
                "host": "localhost",
                "port": 5432,
                "database": "tradefusion",
                "user": "postgres",
                "password": "ymjatTUU520"
            },
            "database_meta": {
                "type": "postgresql",
                "data_path": "数据库_PostgreSQL"
            }
        }
    
    def get(self, key_path: str, default_value: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 'database.host' 或 'data_sources.dzh_base_path'
            default_value: 如果配置不存在时返回的默认值
            
        Returns:
            配置值
        """
        if not self._config_data:
            return default_value
        
        keys = key_path.split('.')
        current = self._config_data
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return default_value
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.get('database', {
            'host': 'localhost',
            'port': 5432,
            'database': 'tradefusion',
            'user': 'postgres',
            'password': 'ymjatTUU520'
        })
    
    def get_dzh_base_path(self) -> str:
        """获取大智慧数据基础路径"""
        return self.get('data_sources.dzh_base_path', 'E:/dzh2/USERDATA/SelfData')
    
    def reload(self):
        """重新加载配置"""
        self._load_config()
        print("🔄 配置已重新加载")


# 全局配置实例
_config_instance: Optional[ConfigManager] = None

def get_config() -> ConfigManager:
    """获取全局配置实例"""
    global _config_instance
    if _config_instance is None:
        _config_instance = ConfigManager()
    return _config_instance

def reload_config():
    """重新加载配置"""
    global _config_instance
    if _config_instance:
        _config_instance.reload()
    else:
        _config_instance = ConfigManager()


if __name__ == "__main__":
    # 测试配置管理器
    config = get_config()
    
    print("📋 配置管理器测试:")
    print(f"数据库配置: {config.get_database_config()}")
    print(f"大智慧路径: {config.get_dzh_base_path()}")
    print(f"批处理大小: {config.get('processing.batch_size')}")
    print(f"时区设置: {config.get('processing.timezone')}")
    
    print("✅ 配置管理器测试完成")
