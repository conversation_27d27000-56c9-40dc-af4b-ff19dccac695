#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块精选模块
功能：基于大智慧SMA算法和最新评分的双重条件筛选精选板块
作者：AI助手
创建时间：2025-07-02
"""

# {{ AURA-X: Modify - 将SQLite连接改为PostgreSQL连接. Approval: 寸止(ID:1737734400). }}
import psycopg2
import psycopg2.extras
from psycopg2.extensions import connection as Connection
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Any
import os
from logging.handlers import TimedRotatingFileHandler

# {{ AURA-X: Modify - 修复导入路径. Approval: 寸止(ID:1737734400). }}
# 添加项目根目录到路径
import sys
from pathlib import Path
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# TradeFusion数据流日志系统 - 低频模块
# 导入TradeFusion统一日志标准
from 公共模块.TradeFusion统一日志标准 import 获取日志器

# 获取统一日志器
logger = 获取日志器("板块精选")


class SectorSelector:
    """板块精选器"""
    
    def __init__(self, db_config: Optional[Dict[str, Any]] = None):
        """
        初始化板块精选器

        Args:
            db_config: PostgreSQL数据库配置，默认为标准配置
        """
        # {{ AURA-X: Modify - 修改为PostgreSQL连接配置. Approval: 寸止(ID:1737734400). }}
        if db_config is None:
            self.db_config = {
                'host': 'localhost',
                'port': 5432,
                'database': 'tradefusion',
                'user': 'postgres',
                'password': 'ymjatTUU520'
            }
        else:
            self.db_config = db_config

        self.sma_period = 5  # SMA计算周期（天）
        self.sma_weight = 1  # SMA权重参数

    def get_db_connection(self) -> Connection:
        """获取数据库连接"""
        # {{ AURA-X: Modify - 修改为PostgreSQL连接方法. Approval: 寸止(ID:1737734400). }}
        try:
            conn = psycopg2.connect(**self.db_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            raise
    
    def create_premium_sectors_table(self):
        """创建板块精选表"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                # {{ AURA-X: Modify - 修改为PostgreSQL语法，使用双引号而非方括号. Approval: 寸止(ID:1737734400). }}
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS "板块精选表" (
                        "日期" INTEGER NOT NULL,
                        "板块名称" TEXT NOT NULL,
                        "综合评分" REAL,
                        PRIMARY KEY ("日期", "板块名称")
                    )
                ''')

                # 创建索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_精选表_日期
                    ON "板块精选表"("日期")
                ''')
                
                conn.commit()
                
        except Exception as e:
            raise
    
    def get_recent_dates(self, days: int = 5) -> List[str]:
        """
        获取最近N个交易日期
        
        Args:
            days: 获取天数
            
        Returns:
            日期列表，格式为YYYYMMDD
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                cursor.execute("""
                    SELECT DISTINCT "日期"
                    FROM "板块涨停表"
                    WHERE "板块评分" > 0
                    ORDER BY "日期" DESC
                    LIMIT %s
                """, (days,))
                
                dates = [str(row[0]) for row in cursor.fetchall()]
                return dates
                
        except Exception as e:
            return []
    
    def get_sector_data_by_dates(self, dates: List[str]) -> Dict[str, List[Dict]]:
        """
        根据日期获取板块数据
        
        Args:
            dates: 日期列表
            
        Returns:
            按板块名称分组的数据字典
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                # 构建日期条件
                date_placeholders = ','.join(['%s' for _ in dates])

                cursor.execute(f"""
                    SELECT "日期", "板块名称", "板块评分"
                    FROM "板块涨停表"
                    WHERE "日期" IN ({date_placeholders})
                    AND "板块评分" > 0
                    ORDER BY "板块名称", "日期" DESC
                """, dates)

                # 按板块名称分组
                sector_data = {}
                for row in cursor.fetchall():
                    sector_name = row[1]  # PostgreSQL返回tuple，不是dict
                    if sector_name not in sector_data:
                        sector_data[sector_name] = []

                    sector_data[sector_name].append({
                        '日期': row[0],
                        '板块名称': row[1],
                        '板块评分': float(row[2])
                    })
                
                return sector_data
                
        except Exception as e:
            return {}
    
    def calculate_dazhihui_sma(self, scores: List[float], n: int = 5, m: int = 1) -> float:
        """
        计算大智慧SMA算法

        公式: SMA(X,N,M) = [M*X + (N-M)*Y'] / N
        其中: X=当前值, N=周期数, M=权重参数, Y'=上一周期SMA值

        Args:
            scores: 评分列表（按时间倒序）
            n: 周期数
            m: 权重参数

        Returns:
            SMA值
        """
        if not scores or len(scores) == 0:
            return 0.0

        # 修复：将数据反转，从最早的数据开始计算
        # 原数据是按时间倒序（最新在前），需要反转为正序（最早在前）
        reversed_scores = scores[::-1]

        # 第一天初始值（现在是最早的数据）
        sma = reversed_scores[0]

        # 递归计算后续值（从最早到最新的顺序）
        for i in range(1, len(reversed_scores)):
            x = reversed_scores[i]
            sma = (m * x + (n - m) * sma) / n

        return round(sma, 2)

    def get_filtered_sector_data(self, recent_5_dates: List[str], past_5_dates: List[str], today_date: str) -> Dict[str, Dict]:
        """
        获取符合过滤条件的板块数据

        过滤条件：只有在过去5天内（不含今天）上榜过至少一次的板块才参与计算
        SMA计算：使用最近5天数据（包含今天）

        Args:
            recent_5_dates: 最近5天的日期列表（包含今天，用于SMA计算）
            past_5_dates: 过去5天的日期列表（不包含今天，用于过滤条件）
            today_date: 今天的日期

        Returns:
            符合条件的板块数据字典
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                # 1. 先获取在过去5天内（不含今天）上榜过的板块名称
                past_date_placeholders = ','.join(['%s' for _ in past_5_dates])
                cursor.execute(f"""
                    SELECT DISTINCT "板块名称"
                    FROM "板块涨停表"
                    WHERE "日期" IN ({past_date_placeholders})
                    AND "板块评分" > 0
                """, past_5_dates)

                qualified_sectors = set([row[0] for row in cursor.fetchall()])

                if not qualified_sectors:
                    return {}

                # 2. 获取这些符合条件板块在最近5天（含今天）的所有数据
                recent_date_placeholders = ','.join(['%s' for _ in recent_5_dates])
                sector_placeholders = ','.join(['%s' for _ in qualified_sectors])

                cursor.execute(f"""
                    SELECT "日期", "板块名称", "板块评分"
                    FROM "板块涨停表"
                    WHERE "日期" IN ({recent_date_placeholders})
                    AND "板块名称" IN ({sector_placeholders})
                    AND "板块评分" > 0
                    ORDER BY "板块名称", "日期" DESC
                """, recent_5_dates + list(qualified_sectors))

                raw_data = cursor.fetchall()

                # 3. 为每个符合条件的板块构建完整的5天数据序列
                complete_data = {}

                for sector_name in qualified_sectors:
                    # 初始化板块数据结构
                    complete_data[sector_name] = {
                        'recent_5_days_scores': [],  # 最近5天的完整评分序列（按时间倒序，包含今天）
                        'today_score': 0.0,          # 今天的评分
                        'sector_name': sector_name
                    }

                    # 收集该板块在最近5天的数据
                    sector_recent_data = {}

                    # {{ AURA-X: Modify - 修改为PostgreSQL tuple访问方式. Approval: 寸止(ID:1737734400). }}
                    for row in raw_data:
                        if row[1] == sector_name:  # PostgreSQL返回tuple，索引1是板块名称
                            date_str = str(row[0])  # 索引0是日期
                            score = float(row[2])   # 索引2是板块评分
                            sector_recent_data[date_str] = score

                    # 构建完整的最近5天序列（按时间倒序，缺失日期赋值0）
                    for date in recent_5_dates:  # recent_5_dates已经是按时间倒序排列
                        if date in sector_recent_data:
                            complete_data[sector_name]['recent_5_days_scores'].append(sector_recent_data[date])
                            if date == today_date:
                                complete_data[sector_name]['today_score'] = sector_recent_data[date]
                        else:
                            complete_data[sector_name]['recent_5_days_scores'].append(0.0)  # 缺失日期赋值0

                    # 调试信息

                return complete_data

        except Exception as e:
            return {}

    def calculate_sector_sma_with_filtered_data(self, filtered_data: Dict[str, Dict], today_date: str) -> List[Dict]:
        """
        使用符合条件的数据计算所有板块的SMA值

        Args:
            filtered_data: 符合条件的板块数据字典
            today_date: 今天的日期

        Returns:
            包含SMA值的板块列表
        """
        sma_sectors = []

        for sector_name, sector_info in filtered_data.items():
            # 使用最近5天的完整评分序列计算SMA（包含今天）
            recent_scores = sector_info['recent_5_days_scores']
            today_score = sector_info['today_score']

            # 计算SMA（使用最近5天数据，包含0值）
            sma_score = self.calculate_dazhihui_sma(recent_scores, self.sma_period, self.sma_weight)

            sma_sectors.append({
                'sector_name': sector_name,
                'sma_score': sma_score,
                'latest_score': today_score,  # 今天的评分作为latest_score
                'daily_scores': recent_scores,  # 最近5天的完整序列（包含今天）
                'today_score': today_score
            })

            # 调试信息

        return sma_sectors

    def get_all_today_sectors(self, today_date: str) -> List[Dict]:
        """
        获取所有今天有评分的板块（不受过滤条件限制）

        Args:
            today_date: 今天的日期

        Returns:
            今天所有有评分的板块列表
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                cursor.execute("""
                    SELECT "板块名称", "板块评分"
                    FROM "板块涨停表"
                    WHERE "日期" = %s AND "板块评分" > 0
                    ORDER BY "板块评分" DESC
                """, (int(today_date),))

                today_sectors = []
                for row in cursor.fetchall():
                    today_sectors.append({
                        'sector_name': row[0],  # PostgreSQL返回tuple
                        'today_score': float(row[1])
                    })

                return today_sectors

        except Exception as e:
            return []

    def calculate_sector_sma(self, sector_data: Dict[str, List[Dict]]) -> List[Dict]:
        """
        计算所有板块的SMA值

        Args:
            sector_data: 板块数据字典

        Returns:
            包含SMA值的板块列表
        """
        sma_sectors = []

        # 获取最新日期（用于正确计算latest_score）
        latest_date = self.get_recent_dates(1)[0]

        for sector_name, daily_data in sector_data.items():
            if len(daily_data) == 0:
                continue

            # 提取评分列表（已按日期倒序排列）
            scores = [item['板块评分'] for item in daily_data]

            # 计算SMA
            sma_score = self.calculate_dazhihui_sma(scores, self.sma_period, self.sma_weight)

            # 获取最新日期的评分（修复bug：确保是最新日期的评分，而不是该板块的最新评分）
            latest_score = 0.0
            for item in daily_data:
                if int(item['日期']) == int(latest_date):
                    latest_score = item['板块评分']
                    break

            sma_sectors.append({
                'sector_name': sector_name,
                'sma_score': sma_score,
                'latest_score': latest_score,
                'daily_scores': scores,
                'daily_data': daily_data
            })

            # 调试信息：显示每个板块的latest_score来源
            if latest_score > 0:
                pass  # 有最新评分
            else:
                pass  # 无最新评分

        return sma_sectors
    
    def select_premium_sectors(self, sma_sectors: List[Dict[str, Any]], all_today_sectors: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        精选板块算法

        条件1: SMA值前四名的板块（在符合过滤条件的板块中）
        条件2: 最新日期的"板块评分"值前三名的板块（在所有今天有评分的板块中）
        精选结果: 条件1 AND 条件2 (交集)

        Args:
            sma_sectors: 包含SMA值的板块列表（已过滤）
            all_today_sectors: 所有今天有评分的板块列表（不受过滤条件限制）

        Returns:
            精选板块列表
        """
        try:

            # 条件1: 按SMA值降序排列（在符合过滤条件的板块中）
            sma_sorted = sorted(sma_sectors, key=lambda x: x['sma_score'], reverse=True)

            # 条件2: 使用所有今天有评分的板块（不受过滤条件限制）
            if all_today_sectors is None:
                # 兼容旧逻辑：如果没有提供all_today_sectors，则使用sma_sectors中有今日评分的板块
                latest_with_data = [s for s in sma_sectors if s['latest_score'] > 0]
                latest_sorted = sorted(latest_with_data, key=lambda x: x['latest_score'], reverse=True)
            else:
                # 新逻辑：使用所有今天有评分的板块
                latest_sorted = sorted(all_today_sectors, key=lambda x: x['today_score'], reverse=True)

            # 调试信息
            for i, sector in enumerate(latest_sorted[:5], 1):
                score = sector.get('latest_score', sector.get('today_score', 0))
                sector_name = sector.get('sector_name', '未知')

            # 条件1: SMA值前四名的板块名称
            top_sma_names = set([sector['sector_name'] for sector in sma_sorted[:4]])

            # 条件2: 最新日期评分前三名的板块名称
            if all_today_sectors is None:
                # 兼容旧逻辑
                top_latest_names = set([sector['sector_name'] for sector in latest_sorted[:3]])
            else:
                # 新逻辑：从所有今天有评分的板块中选择前3名
                top_latest_names = set([sector['sector_name'] for sector in latest_sorted[:3]])

            # 计算交集：同时满足两个条件的板块
            intersection_names = top_sma_names & top_latest_names

            # 获取交集板块的详细信息
            selected_sectors = []
            for sector in sma_sectors:
                if sector['sector_name'] in intersection_names:
                    # 获取在各自排名中的位置
                    sma_rank = next(i+1 for i, s in enumerate(sma_sorted) if s['sector_name'] == sector['sector_name'])

                    # 在latest_sorted中查找排名（需要处理不同的数据结构）
                    latest_rank = None
                    for i, s in enumerate(latest_sorted):
                        s_name = s.get('sector_name', s.get('sector_name', ''))
                        if s_name == sector['sector_name']:
                            latest_rank = i + 1
                            break

                    if latest_rank is None:
                        latest_rank = 999  # 如果找不到，设置一个大数

                    sector_info = sector.copy()
                    sector_info.update({
                        'selection_reason': f'SMA第{sma_rank}名 & 最新第{latest_rank}名',
                        'display_score': sector['sma_score'],
                        'score_type': 'SMA',
                        'sma_rank': sma_rank,
                        'latest_rank': latest_rank
                    })
                    selected_sectors.append(sector_info)
            
            # 按SMA值降序排列
            selected_sectors.sort(key=lambda x: x['sma_score'], reverse=True)
            
            
            # 打印精选结果
            for i, sector in enumerate(selected_sectors):
                pass  # 精选结果处理

            return selected_sectors
            
        except Exception as e:
            return []
    
    def save_premium_sectors_to_db(self, selected_sectors: List[Dict[str, Any]], date: str, all_sectors: Optional[List[Dict[str, Any]]] = None):
        """
        保存精选板块到数据库，并删除不再符合条件的板块

        Args:
            selected_sectors: 精选板块列表
            date: 日期（YYYYMMDD格式）
            all_sectors: 所有板块列表（用于判断哪些板块不再符合条件）
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                # 获取当前数据库中该日期的所有精选板块
                cursor.execute('SELECT "板块名称" FROM "板块精选表" WHERE "日期" = %s', (int(date),))
                existing_sectors = set([row[0] for row in cursor.fetchall()])

                # 获取新的精选板块名称
                new_selected_names = set([sector['sector_name'] for sector in selected_sectors])

                # 找出需要删除的板块（存在于数据库但不在新精选列表中）
                sectors_to_remove = existing_sectors - new_selected_names

                # 删除不再符合条件的板块
                if sectors_to_remove:
                    for sector_name in sectors_to_remove:
                        cursor.execute("""
                            DELETE FROM "板块精选表"
                            WHERE "日期" = %s AND "板块名称" = %s
                        """, (int(date), sector_name))

                # 更新或插入新的精选板块
                for sector in selected_sectors:
                    # 检查板块是否已存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM "板块精选表"
                        WHERE "日期" = %s AND "板块名称" = %s
                    """, (int(date), sector['sector_name']))

                    exists = cursor.fetchone()[0] > 0

                    if exists:
                        # 更新现有记录
                        cursor.execute("""
                            UPDATE "板块精选表"
                            SET "综合评分" = %s
                            WHERE "日期" = %s AND "板块名称" = %s
                        """, (sector['sma_score'], int(date), sector['sector_name']))
                    else:
                        # 插入新记录
                        cursor.execute("""
                            INSERT INTO "板块精选表" ("日期", "板块名称", "综合评分")
                            VALUES (%s, %s, %s)
                        """, (int(date), sector['sector_name'], sector['sma_score']))

                conn.commit()

                # 统计结果
                added_sectors = new_selected_names - existing_sectors
                updated_sectors = new_selected_names & existing_sectors


        except Exception as e:
            raise
    
    def get_premium_sectors_from_db(self, date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        从数据库获取精选板块
        
        Args:
            date: 日期，如果为None则获取最新日期的数据
            
        Returns:
            精选板块列表
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                if date is None:
                    # 获取最新日期的数据
                    cursor.execute("""
                        SELECT "日期", "板块名称", "综合评分"
                        FROM "板块精选表"
                        WHERE "日期" = (SELECT MAX("日期") FROM "板块精选表")
                        ORDER BY "综合评分" DESC
                    """)
                else:
                    # 获取指定日期的数据
                    cursor.execute("""
                        SELECT "日期", "板块名称", "综合评分"
                        FROM "板块精选表"
                        WHERE "日期" = %s
                        ORDER BY "综合评分" DESC
                    """, (int(date),))

                sectors = []
                for row in cursor.fetchall():
                    sectors.append({
                        'date': str(row[0]),  # PostgreSQL返回tuple
                        'sector_name': row[1],
                        'comprehensive_score': row[2]
                    })
                
                return sectors
                
        except Exception as e:
            return []

    def calculate_and_save_premium_sectors(self) -> Dict:
        """
        计算并保存精选板块

        Returns:
            包含计算结果的字典
        """
        try:
            # 移除开始日志，只保留完成日志

            # 确保表存在
            self.create_premium_sectors_table()

            # 获取最近6个交易日期
            all_dates = self.get_recent_dates(6)
            if len(all_dates) < 6:
                error_msg = f'交易日期不足，需要6个日期，实际获取到{len(all_dates)}个'
                logger.error(f"❌ \033[91m[板块精选]\033[0m {error_msg}")
                return {'error': error_msg}

            # 分离日期：今天 + 最近5天（包含今天）vs 过去5天（不含今天，用于过滤）
            today_date = all_dates[0]  # 今天（最新日期）
            recent_5_dates = all_dates[0:5]  # 最近5天（包含今天，用于SMA计算）
            past_5_dates = all_dates[1:6]  # 过去5天（不包含今天，用于过滤条件）


            # 获取符合条件的板块数据
            sector_data = self.get_filtered_sector_data(recent_5_dates, past_5_dates, today_date)
            if not sector_data:
                return {'error': '无法获取板块数据'}

            # 计算SMA值
            sma_sectors = self.calculate_sector_sma_with_filtered_data(sector_data, today_date)
            if not sma_sectors:
                return {'error': '无法计算SMA值'}

            # 精选板块（需要获取所有今天有评分的板块用于条件2）
            all_today_sectors = self.get_all_today_sectors(today_date)
            selected_sectors = self.select_premium_sectors(sma_sectors, all_today_sectors)

            # 保存到数据库（使用今天日期）
            self.save_premium_sectors_to_db(selected_sectors, today_date, sma_sectors)

            # 记录处理结果（被调用者后输出）
            logger.info(f"\033[91m[板块精选]\033[0m SMA算法精选完成 - \033[93m板块精选表\033[0m+\033[92m{len(selected_sectors)}个\033[0m精选板块 (由\033[91m[板块涨停表]\033[0m调用)")

            return {
                'success': True,
                'date': today_date,
                'selected_sectors': selected_sectors,
                'total_sectors': len(sma_sectors),
                'selected_count': len(selected_sectors),
                'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            return {'error': str(e)}

    def get_premium_sectors_api(self) -> Dict:
        """
        获取精选板块API数据

        Returns:
            API格式的精选板块数据
        """
        try:
            # 从数据库获取精选板块
            sectors = self.get_premium_sectors_from_db()

            if not sectors:
                # 如果数据库中没有数据，则计算并保存
                result = self.calculate_and_save_premium_sectors()
                if 'error' in result:
                    return result
                sectors = self.get_premium_sectors_from_db()

            return {
                'success': True,
                'data': sectors,
                'count': len(sectors),
                'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            return {'error': str(e)}

    def clean_outdated_premium_sectors(self, days_to_keep: int = 30) -> Dict:
        """
        清理过期的精选板块数据，只保留最近N天的数据

        Args:
            days_to_keep: 保留的天数

        Returns:
            清理结果字典
        """
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                # 获取需要保留的日期列表
                cursor.execute("""
                    SELECT DISTINCT "日期"
                    FROM "板块精选表"
                    ORDER BY "日期" DESC
                    LIMIT %s
                """, (days_to_keep,))

                keep_dates = [row[0] for row in cursor.fetchall()]

                if not keep_dates:
                    return {'success': True, 'deleted_count': 0, 'message': '没有数据需要清理'}

                # 删除不在保留列表中的数据
                keep_dates_placeholders = ','.join(['%s' for _ in keep_dates])
                cursor.execute(f"""
                    DELETE FROM "板块精选表"
                    WHERE "日期" NOT IN ({keep_dates_placeholders})
                """, keep_dates)

                deleted_count = cursor.rowcount
                conn.commit()


                return {
                    'success': True,
                    'deleted_count': deleted_count,
                    'kept_dates': len(keep_dates),
                    'message': f'成功清理{deleted_count}条过期数据'
                }

        except Exception as e:
            return {'error': str(e)}

    def validate_premium_sectors_consistency(self) -> Dict:
        """
        验证精选板块数据的一致性，检查是否有不符合条件的板块

        Returns:
            验证结果字典
        """
        try:

            # 获取最近6个交易日期（使用新的逻辑）
            all_dates = self.get_recent_dates(6)
            if len(all_dates) < 6:
                return {'error': f'交易日期不足，需要6个日期，实际获取到{len(all_dates)}个'}

            # 分离日期
            today_date = all_dates[0]
            recent_5_dates = all_dates[0:5]
            past_5_dates = all_dates[1:6]

            # 使用新的逻辑重新计算精选板块
            sector_data = self.get_filtered_sector_data(recent_5_dates, past_5_dates, today_date)
            if not sector_data:
                return {'error': '无法获取板块数据'}

            sma_sectors = self.calculate_sector_sma_with_filtered_data(sector_data, today_date)
            if not sma_sectors:
                return {'error': '无法计算SMA值'}

            # 获取所有今天有评分的板块用于条件2
            all_today_sectors = self.get_all_today_sectors(today_date)
            current_selected = self.select_premium_sectors(sma_sectors, all_today_sectors)
            current_selected_names = set([s['sector_name'] for s in current_selected])

            # 获取数据库中的精选板块
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                # {{ AURA-X: Modify - 修改为PostgreSQL语法. Approval: 寸止(ID:1737734400). }}
                cursor.execute('SELECT "板块名称" FROM "板块精选表" WHERE "日期" = %s', (int(today_date),))
                db_selected_names = set([row[0] for row in cursor.fetchall()])

            # 比较差异
            missing_in_db = current_selected_names - db_selected_names
            extra_in_db = db_selected_names - current_selected_names

            result = {
                'success': True,
                'date': today_date,
                'current_selected': len(current_selected_names),
                'db_selected': len(db_selected_names),
                'missing_in_db': list(missing_in_db),
                'extra_in_db': list(extra_in_db),
                'is_consistent': len(missing_in_db) == 0 and len(extra_in_db) == 0
            }

            if result['is_consistent']:
                pass  # 数据一致
            else:
                pass  # 数据不一致

            return result

        except Exception as e:
            logger.error(f"❌ \033[91m[板块精选]\033[0m SMA算法计算失败: {str(e)}")
            return {'error': str(e)}


def main():
    """主函数，供其他模块调用"""
    selector = SectorSelector()

    # 计算并保存精选板块
    result = selector.calculate_and_save_premium_sectors()

    if 'error' in result:
        logger.error(f"✗ <板块精选> 执行失败: {result['error']}")
        return False
    else:
        # 成功日志已在函数内部记录
        return True


if __name__ == "__main__":
    main()
